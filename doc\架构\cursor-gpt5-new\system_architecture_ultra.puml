@startuml system_architecture_ultra
title 中兴 IBO-ACM 产品检索系统 架构图（UltraThink）
caption 以代码仓为依据的高层系统视图（组件/层次/外部依赖/观测性）

skinparam backgroundColor #FFFFFF
skinparam defaultFontName Microsoft YaHei
skinparam componentStyle rectangle
skinparam ArrowColor #555555
skinparam componentBorderColor #888888
skinparam componentBackgroundColor #F8F9FB
skinparam packageBorderColor #666666
skinparam packageBackgroundColor #FFFFFF
skinparam rectangleBorderColor #888888
skinparam noteBackgroundColor #FFFAE6
skinparam noteBorderColor #F2C94C
skinparam LegendBackgroundColor #F8F8F8
skinparam LegendBorderColor #DDDDDD

left to right direction

rectangle "表示层（UI / 接口）" as layer_ui {
  component "UI 测试工具\n`ui/ui_for_test_infoGo.py`\n`ui/ui_for_test_infoGo2_23prod.py`" as UI_Test
  component "本地调试/演示\n`demo_monitor.py`\n`view_monitor.py`" as UI_Demo
  component "API 调用脚本\n`test_api.py`" as API_Scripts
}

rectangle "接口层（Controller）" as layer_controller {
  component "监控接口\n`controller/monitor_controller.py`" as Ctr_Monitor
  component "业务接口\n`controller/zxtech_controller.py`" as Ctr_ZXTech
}

rectangle "服务层（Service / Orchestrator）" as layer_service {
  component "检索编排服务\n`service/zxtech_service.py`" as Svc_ZX
  component "监控装饰器\n`service/monitor_decorator.py`" as Svc_MonitorDecorator
}

rectangle "检索与NLP能力层" as layer_nlp {
  component "Milvus 召回\n`retrieve/milvus_recall.py`" as Recall_Milvus
  component "知识图谱召回\n`retrieve/kg_retrieve/kg_retrieve.py`\n`kg_retrieve/entity_link.py`" as Recall_KG
  component "签名/去重\n`utils/generate_signature.py`\n`retrieve/kg_retrieve/generate_signature.py`" as Dedup_Sign
  component "Embedding 向量化\n`embedding/get_embedding.py`" as Embedding
  component "重排（Rerank）\n`rerank/bge_m3_reranker_v2.py`\n`rerank/rrf.py`" as Rerank
  component "重写（Rewrite）\n`rewrite/rewrite_model.py`" as Rewrite
  component "提示词管理\n`prompt/PromptLoader.py`\n`prompt/prompt_instance/*`" as Prompt
}

rectangle "通用支撑（Utils / LLM / 日志）" as layer_utils {
  component "LLM 能力封装\n`utils/llm_util/llm_util.py`\n`utils/llm_util/llm_param.py`\n`llm/LLMResult.py`" as LLM_Util
  component "相似度/实体工具\n`utils/cos_sim.py`\n`utils/entity_util.py`\n`utils/recall_context.py`" as Common_Utils
  component "日志&追踪\n`resource/full_link_log_setup.py`\n`utils/logger/*`\n`logs/*`" as Logging
}

rectangle "配置与基础设施（Infra）" as layer_infra {
  component "配置中心（Apollo）\n`apollo/*`\n`infrastructure/config/config_center.py`" as Apollo
  component "本地配置\n`config.py`\n`domain/config/zxtech_config.py`\n`service_setting.cfg`" as Config_Local
  component "代理/网络\n`proxy_config.py`" as Proxy
}

rectangle "数据与外部系统" as layer_data {
  database "Milvus 向量库\n`utils/milvus_util/*`" as DB_Milvus
  database "Elasticsearch（可选）\n`utils/es_util/es_util.py`" as DB_ES
  cloud "LLM 提供方（本地/云）" as Cloud_LLM
}

rectangle "观测与质量（Monitoring）" as layer_monitor {
  component "召回质量监控\n`monitor/recall_monitor.py`\n`monitor_data/*`" as Monitor_Recall
  component "分析与报告\n`analyze_recall_contribution.py`\n`recall_analysis_report.md`" as Monitor_Analyze
}

UI_Test --> Ctr_ZXTech : HTTP/CLI 调用
UI_Demo --> Ctr_Monitor : 本地演示/监控查询
API_Scripts --> Ctr_ZXTech : API 调用

Ctr_ZXTech --> Svc_ZX : 请求进入编排服务
Ctr_Monitor --> Svc_ZX : 监控调用（共享编排）
Svc_ZX ..> Svc_MonitorDecorator : 统一观测

Svc_ZX --> Prompt : 加载提示词
Svc_ZX --> Embedding : 生成查询向量
Svc_ZX --> Recall_Milvus : 语义召回
Svc_ZX --> Recall_KG : 实体/关系召回
Svc_ZX --> Dedup_Sign : 结果签名/合并
Svc_ZX --> Rerank : 相关性重排
Svc_ZX --> Rewrite : 答案重写/结构化
Rewrite --> LLM_Util : 调用 LLM
LLM_Util --> Cloud_LLM : 推理

Recall_Milvus --> DB_Milvus : 向量相似度检索
Recall_KG ..> DB_ES : 知识/索引查询（可选）

Apollo ..> Config_Local : 动态配置下发
Svc_ZX ..> Config_Local : 读取运行参数
Svc_ZX ..> Logging : 结构化日志
layer_nlp ..> Logging : 组件日志/耗时
Monitor_Recall ..> Logging : 采集日志
Monitor_Recall --> Monitor_Analyze : 数据分析/报告

Proxy ..> Cloud_LLM : 代理转发（如需）

note right of Svc_ZX
编排主要步骤：
1) 解析问题，加载 Prompt
2) 生成向量，Milvus/图谱召回
3) 合并去重，重排
4) 调用 LLM 重写/生成
5) 返回结构化结果并记录日志/指标
end note

legend left
  颜色/形状说明：
  [rectangle] 应用组件/服务
  [database] 数据存储/索引
  [cloud] 外部云/推理服务
  [note] 过程说明
endlegend

@enduml






