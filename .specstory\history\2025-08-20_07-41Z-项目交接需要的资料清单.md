<!-- Generated by SpecStory -->

# 项目交接需要的资料清单 (2025-08-20 07:41Z)

_**User**_

这个项目是刚交接过来的，我需要他们交接给我哪些东西？
可能有这些东西：
1、 代码  
2、需求、方案文档   
3、工具：1）infogo数据处理脚本，2）文档信息处理脚本   3）转KG的脚本   4）解析切分脚本； 
4、系统账号、密码


---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户刚接手了一个项目，想知道需要交接哪些东西。从项目结构来看，这是一个AI/RAG系统的项目，涉及知识图谱、搜索、重排序等功能。

用户提到的需要交接的内容包括：
1. 代码
2. 需求、方案文档
3. 工具脚本
4. 系统账号密码

我需要分析项目结构，查看现有的文档和代码，帮助用户整理出完整的交接清单。

从项目结构可以看到：
- 有代码结构（各种服务、工具类、配置等）
- 有文档目录（doc/）
- 有一些脚本和工具
- 有配置文件

我需要详细分析这些内容，给出完整的交接清单。</details></think>

---

我来帮你分析这个项目的交接内容。让我先查看项目的整体情况和相关文档。

---

Could not find file 'README.md' in the workspace.

---

Read file: undefined

---

Read file: undefined

---

<details>
            <summary>Listed directory undefined • **0** results</summary>
        
No results found
</details>

---

<think><details><summary>Thought Process</summary>现在我看到了主要的项目信息。让我继续查看一些关键的配置文件和文档，以便给出更完整的交接清单。</details></think>

---

现在让我查看一些关键的配置文件和文档，以及项目的核心功能模块：

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

Read file: undefined

---

<details>
            <summary>Listed directory undefined • **0** results</summary>
        
No results found
</details>

---

<think><details><summary>Thought Process</summary>让我再看一些关键脚本和配置文件，以便给出更完整的交接清单。</details></think>

---

让我继续查看一些关键的脚本和工具，以及系统配置信息：

---

Read file: undefined

---

<details>
            <summary>Listed directory undefined • **0** results</summary>
        
No results found
</details>

---

Read file: undefined

---

Read file: undefined

---

<think><details><summary>Thought Process</summary>看起来我已经收集了足够的信息来给用户提供一个完整的交接清单。让我整理一下这个项目需要交接的所有内容。

这是一个ZTE的AI产品问答系统，基于RAG架构，支持多种数据源检索和知识图谱查询。从项目结构和配置可以看出：

1. 代码结构完整，包含各个功能模块
2. 有详细的技术文档
3. 有监控系统
4. 有各种配置文件和脚本
5. 需要多个外部系统的账号和配置

让我创建一个详细的交接清单。</details></think>

---

基于我对项目的分析，我为你整理了一个完整的交接清单。这是一个ZTE的AI产品问答系统，基于RAG架构。以下是需要交接的所有内容：

## 📋 ZTE产品技术问答系统交接清单

### 1️⃣ **代码交接**
✅ **已有的代码模块：**
- **主服务**：`main.py` (Flask应用，监听端口10023)
- **控制器**：`controller/` (接口控制器)
- **业务服务**：`service/` (核心业务逻辑)  
- **数据检索**：`retrieve/` (ES、Milvus、知识图谱检索)
- **重排序**：`rerank/` (BGE-M3重排序模型)
- **工具类**：`utils/` (各种工具函数)
- **领域模型**：`domain/` (业务实体和配置)

### 2️⃣ **需求方案文档交接** 
✅ **已有文档：**
- **需求方案**：`doc/架构/演进方向/需求方案.md`
- **技术架构**：`doc/架构/` (包含多版本架构图)
- **技术选型报告**：`doc/技术选型/` (各AI助手版本报告)
- **开发设计文档**：`doc/架构/演进方向/aug/` 
- **监控系统说明**：`监控系统使用说明.md`

❓ **需确认交接：**
- 最终确认的技术方案版本
- 业务需求变更历史
- 测试验收标准文档

### 3️⃣ **工具脚本交接**

✅ **已有脚本：**
1. **InfoGo数据处理**：`utils/infoGo_util.py`
2. **文档处理脚本**：`resource/folder_index_dev.py` 
3. **KG检索脚本**：`retrieve/kg_retrieve/kg_retrieve.py`
4. **实体链接脚本**：`retrieve/kg_retrieve/entity_link.py`

❓ **需确认交接：**
- TSM文档同步脚本
- 文档切分和向量化脚本  
- 数据预处理和清洗脚本
- 批量数据导入脚本

### 4️⃣ **系统账号密码交接**

❌ **需要获取：**

**LLM服务账号：**
- 大模型API Token: `config['LLM']['llm_headers']['Authorization']` (已加密)
- 员工号: `config['LLM']['llm_headers']['X-Emp-No']`

**ElasticSearch服务：**
- ES访问密钥: `config['Knowledge']['access_key']` (已加密)
- ES访问秘钥: `config['Knowledge']['access_secret']` (已加密) 
- ES服务地址: `https://kger.zte.com.cn/zte-dmt-dmp-kgbff/...`

**知识图谱服务：**
- KG服务地址: `https://kger.zte.com.cn/zte-dmt-dmp-kgbff/...`
- 知识空间: `product_multiversion`

**Milvus向量数据库：**
- 主机: `10.136.152.152:19530`
- 集合名: `productQA_1118`

**UAC统一认证：**
- App ID: `612791211526`
- 访问密钥和秘钥 (已加密存储)
- 验证服务地址: `https://uactest.zte.com.cn:5555/...`

**KMS密钥管理：**
- KMS域名: `https://kms-msp.test.zte.com.cn` 
- 密钥类型: `apoll:test:product_key`
- 数据密钥 (已加密)

**Kafka消息队列：**
- Broker地址: `10.54.158.92:9092, 10.54.158.93:9092, 10.54.158.94:9092`
- Topic: `acm-bk-app-nj`

### 5️⃣ **环境配置交接**

❓ **需要获取：**
- **配置解密密钥**：用于解密配置文件中的敏感信息
- **Apollo配置中心**：配置管理平台账号
- **开发/测试/生产环境**：各环境的配置差异说明
- **部署脚本**：Docker部署配置和启动脚本

### 6️⃣ **数据资源交接**

❓ **需要获取：**
- **测试数据集**：`doc/测试集/自建_业务方测试集auto_Qwen2_0723_dev.xlsx`
- **语料数据源**：TSM文档库访问权限
- **InfoGo系统**：数据同步账号
- **停用词表**：`resource/ZTE_stopwords.txt`
- **NLP模型**：`resource/spacy_ontonotes/`

### 7️⃣ **监控运维交接**

✅ **已有监控：**
- 召回监控系统 (监控ES、KG、Milvus召回效果)
- 日志系统 (存储在`logs/`和`resource/logs/`)
- 性能监控数据 (`monitor_data/`)

❓ **需要了解：**
- 告警机制和联系人
- 日志轮转策略
- 性能指标阈值
- 故障处理流程

### 8️⃣ **开发工具交接**

✅ **项目工具：**
- Python环境：requirements.txt已定义依赖
- PlantUML：架构图绘制工具 (`install_plantuml.bat`)
- 监控工具：`view_monitor.py`, `pkl_viewer.py`

## 🚨 **重要提醒**

1. **配置解密**：所有敏感配置都已加密，需要获取KMS解密密钥
2. **网络访问**：系统依赖多个内网服务，需要网络权限配置  
3. **数据权限**：需要TSM、InfoGo等系统的数据访问权限
4. **环境差异**：目前看到的是dev环境配置，需要了解其他环境差异

建议按照以上清单逐项确认交接，特别是加密配置的解密方法和各系统的访问权限。

---

_**User**_

简明写个需要他们交接的清单列表

---

_**Assistant**_



---

